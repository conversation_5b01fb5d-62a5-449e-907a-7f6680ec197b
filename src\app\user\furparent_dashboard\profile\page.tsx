'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CameraIcon,
  PencilIcon,
  XMarkIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import withUserAuth, { UserData } from '@/components/withUserAuth';
import Image from 'next/image';
import { getImagePath } from '@/utils/imageUtils';
import PhilippinePhoneInput from '@/components/ui/PhilippinePhoneInput';
import {
  ProfileLayout,
  ProfileSection,
  ProfileCard,
  ProfileField,
  ProfileFormGroup,
  ProfileGrid
} from '@/components/ui/ProfileLayout';
import {
  ProfileInput,
  ProfileButton,
  ProfileAlert
} from '@/components/ui/ProfileFormComponents';

interface ProfilePageProps {
  userData?: UserData;
}

function ProfilePage({ userData }: ProfilePageProps) {
  console.log('🔍 [ProfilePage] Component rendered with userData:', userData);

  // Loading and error states
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Edit mode states
  const [isEditingPersonal, setIsEditingPersonal] = useState(false);
  const [isEditingContact, setIsEditingContact] = useState(false);

  // Form states
  const [personalInfo, setPersonalInfo] = useState({
    firstName: '',
    lastName: ''
  });
  const [contactInfo, setContactInfo] = useState({
    email: '',
    phone: '',
    address: ''
  });

  // Profile picture states
  const [profilePicture, setProfilePicture] = useState<File | null>(null);
  const [profilePicturePreview, setProfilePicturePreview] = useState<string | null>(null);
  const [uploadingProfilePicture, setUploadingProfilePicture] = useState(false);
  const [profilePictureTimestamp, setProfilePictureTimestamp] = useState<number>(Date.now());
  const profilePictureInputRef = useRef<HTMLInputElement>(null);

  // Loading states for individual sections
  const [isUpdatingPersonal, setIsUpdatingPersonal] = useState(false);
  const [isUpdatingContact, setIsUpdatingContact] = useState(false);

  if (!userData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading profile...</p>
          <p className="text-gray-500 text-sm mt-2">Please wait while we fetch your information...</p>
        </div>
      </div>
    );
  }

  // Initialize form data when userData changes
  useEffect(() => {
    if (userData) {
      setPersonalInfo({
        firstName: userData.first_name || '',
        lastName: userData.last_name || ''
      });
      setContactInfo({
        email: userData.email || '',
        phone: userData.phone || '',
        address: userData.address || ''
      });
    }
  }, [userData]);

  // Clear messages after 5 seconds
  useEffect(() => {
    if (success || error) {
      const timer = setTimeout(() => {
        setSuccess(null);
        setError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [success, error]);

  // Handle profile picture selection
  const handleProfilePictureChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setError('Profile picture must be less than 5MB');
        return;
      }

      if (!file.type.startsWith('image/')) {
        setError('Please select a valid image file');
        return;
      }

      setProfilePicture(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfilePicturePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Upload profile picture
  const uploadProfilePicture = async () => {
    if (!profilePicture) return;

    setUploadingProfilePicture(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('profilePicture', profilePicture);

      const response = await fetch(`/api/users/${userData.user_id || userData.id}/profile-picture`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload profile picture');
      }

      const result = await response.json();
      if (result.success) {
        setSuccess('Profile picture updated successfully!');
        setProfilePicture(null);
        setProfilePicturePreview(null);
        setProfilePictureTimestamp(Date.now());

        // Trigger user data update event
        window.dispatchEvent(new CustomEvent('userDataUpdated', {
          detail: { ...userData, profile_picture: result.profilePicture }
        }));
      }
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      setError('Failed to upload profile picture. Please try again.');
    } finally {
      setUploadingProfilePicture(false);
    }
  };

  // Handle personal info update
  const handlePersonalInfoUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdatingPersonal(true);
    setError(null);

    try {
      const response = await fetch(`/api/users/${userData.user_id || userData.id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          first_name: personalInfo.firstName,
          last_name: personalInfo.lastName,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update personal information');
      }

      const result = await response.json();
      if (result.success) {
        setSuccess('Personal information updated successfully!');
        setIsEditingPersonal(false);

        // Trigger user data update event
        window.dispatchEvent(new CustomEvent('userDataUpdated', {
          detail: { ...userData, first_name: personalInfo.firstName, last_name: personalInfo.lastName }
        }));
      }
    } catch (error) {
      console.error('Error updating personal info:', error);
      setError('Failed to update personal information. Please try again.');
    } finally {
      setIsUpdatingPersonal(false);
    }
  };

  // Handle contact info update
  const handleContactInfoUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdatingContact(true);
    setError(null);

    try {
      const response = await fetch(`/api/users/${userData.user_id || userData.id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: contactInfo.email,
          phone: contactInfo.phone,
          address: contactInfo.address,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update contact information');
      }

      const result = await response.json();
      if (result.success) {
        setSuccess('Contact information updated successfully!');
        setIsEditingContact(false);

        // Trigger user data update event
        window.dispatchEvent(new CustomEvent('userDataUpdated', {
          detail: { ...userData, email: contactInfo.email, phone: contactInfo.phone, address: contactInfo.address }
        }));
      }
    } catch (error) {
      console.error('Error updating contact info:', error);
      setError('Failed to update contact information. Please try again.');
    } finally {
      setIsUpdatingContact(false);
    }
  };

  return (
    <ProfileLayout
      title="My Profile"
      subtitle="Manage your personal information and account settings"
      icon={<UserIcon className="h-8 w-8 text-white" />}
      className="p-6"
    >
      {/* Success/Error Messages */}
      {success && (
        <ProfileAlert
          type="success"
          message={success}
          onClose={() => setSuccess(null)}
        />
      )}

      {error && (
        <ProfileAlert
          type="error"
          message={error}
          onClose={() => setError(null)}
        />
      )}

      {/* Profile Header Section */}
      <ProfileSection
        title="Profile Picture"
        subtitle="Upload a profile picture to personalize your account"
      >
        <ProfileCard>
          <div className="flex items-center space-x-6">
            <div className="relative">
              <div className="w-24 h-24 rounded-full overflow-hidden bg-gray-100 border-4 border-white shadow-lg">
                {userData.profile_picture ? (
                  <Image
                    src={getImagePath(userData.profile_picture, profilePictureTimestamp)}
                    alt="Profile"
                    width={96}
                    height={96}
                    className="w-full h-full object-cover"
                  />
                ) : profilePicturePreview ? (
                  <Image
                    src={profilePicturePreview}
                    alt="Profile Preview"
                    width={96}
                    height={96}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                    <UserIcon className="h-12 w-12 text-white" />
                  </div>
                )}
              </div>

              <button
                onClick={() => profilePictureInputRef.current?.click()}
                className="absolute -bottom-2 -right-2 bg-green-600 text-white p-2 rounded-full shadow-lg hover:bg-green-700 transition-colors duration-200"
                disabled={uploadingProfilePicture}
              >
                <CameraIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">
                {userData.first_name || 'User'} {userData.last_name || ''}
              </h3>
              <p className="text-gray-600">{userData.email}</p>
              <p className="text-sm text-gray-500 mt-1">Pet Parent Account</p>

              {profilePicture && (
                <div className="mt-4 flex items-center space-x-3">
                  <ProfileButton
                    onClick={uploadProfilePicture}
                    loading={uploadingProfilePicture}
                    variant="primary"
                    size="sm"
                  >
                    Upload Picture
                  </ProfileButton>
                  <ProfileButton
                    onClick={() => {
                      setProfilePicture(null);
                      setProfilePicturePreview(null);
                    }}
                    variant="secondary"
                    size="sm"
                  >
                    Cancel
                  </ProfileButton>
                </div>
              )}
            </div>
          </div>

          <input
            ref={profilePictureInputRef}
            type="file"
            accept="image/*"
            onChange={handleProfilePictureChange}
            className="hidden"
          />
        </ProfileCard>
      </ProfileSection>

      {/* Personal Information Section */}
      <ProfileSection
        title="Personal Information"
        subtitle="Your basic personal details"
        action={
          <ProfileButton
            onClick={() => setIsEditingPersonal(!isEditingPersonal)}
            variant="secondary"
            size="sm"
            icon={isEditingPersonal ? <XMarkIcon className="h-4 w-4" /> : <PencilIcon className="h-4 w-4" />}
          >
            {isEditingPersonal ? 'Cancel' : 'Edit'}
          </ProfileButton>
        }
      >
        <ProfileCard>
          {isEditingPersonal ? (
            <form onSubmit={handlePersonalInfoUpdate}>
              <ProfileFormGroup>
                <ProfileGrid cols={2}>
                  <ProfileInput
                    label="First Name"
                    value={personalInfo.firstName}
                    onChange={(value) => setPersonalInfo(prev => ({ ...prev, firstName: value }))}
                    required
                    icon={<UserIcon className="h-5 w-5" />}
                  />
                  <ProfileInput
                    label="Last Name"
                    value={personalInfo.lastName}
                    onChange={(value) => setPersonalInfo(prev => ({ ...prev, lastName: value }))}
                    required
                    icon={<UserIcon className="h-5 w-5" />}
                  />
                </ProfileGrid>
              </ProfileFormGroup>

              <div className="flex justify-end pt-4 border-t border-gray-100">
                <ProfileButton
                  type="submit"
                  variant="primary"
                  loading={isUpdatingPersonal}
                  icon={<CheckCircleIcon className="h-5 w-5" />}
                >
                  {isUpdatingPersonal ? 'Updating...' : 'Update Personal Information'}
                </ProfileButton>
              </div>
            </form>
          ) : (
            <ProfileGrid cols={2}>
              <ProfileField
                label="First Name"
                value={userData.first_name || 'Not provided'}
                icon={<UserIcon className="h-5 w-5" />}
              />
              <ProfileField
                label="Last Name"
                value={userData.last_name || 'Not provided'}
                icon={<UserIcon className="h-5 w-5" />}
              />
            </ProfileGrid>
          )}
        </ProfileCard>
      </ProfileSection>

      {/* Contact Information Section */}
      <ProfileSection
        title="Contact Information"
        subtitle="Your contact details and address"
        action={
          <ProfileButton
            onClick={() => setIsEditingContact(!isEditingContact)}
            variant="secondary"
            size="sm"
            icon={isEditingContact ? <XMarkIcon className="h-4 w-4" /> : <PencilIcon className="h-4 w-4" />}
          >
            {isEditingContact ? 'Cancel' : 'Edit'}
          </ProfileButton>
        }
      >
        <ProfileCard>
          {isEditingContact ? (
            <form onSubmit={handleContactInfoUpdate}>
              <ProfileFormGroup>
                <ProfileInput
                  label="Email Address"
                  type="email"
                  value={contactInfo.email}
                  onChange={(value) => setContactInfo(prev => ({ ...prev, email: value }))}
                  required
                  icon={<EnvelopeIcon className="h-5 w-5" />}
                />

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Phone Number
                  </label>
                  <PhilippinePhoneInput
                    value={contactInfo.phone}
                    onChange={(value) => setContactInfo(prev => ({ ...prev, phone: value }))}
                    placeholder="Enter your phone number"
                  />
                </div>

                <ProfileInput
                  label="Address"
                  value={contactInfo.address}
                  onChange={(value) => setContactInfo(prev => ({ ...prev, address: value }))}
                  placeholder="Enter your complete address"
                  icon={<MapPinIcon className="h-5 w-5" />}
                />
              </ProfileFormGroup>

              <div className="flex justify-end pt-4 border-t border-gray-100">
                <ProfileButton
                  type="submit"
                  variant="primary"
                  loading={isUpdatingContact}
                  icon={<CheckCircleIcon className="h-5 w-5" />}
                >
                  {isUpdatingContact ? 'Updating...' : 'Update Contact Information'}
                </ProfileButton>
              </div>
            </form>
          ) : (
            <ProfileGrid cols={1}>
              <ProfileField
                label="Email Address"
                value={userData.email || 'Not provided'}
                icon={<EnvelopeIcon className="h-5 w-5" />}
              />
              <ProfileField
                label="Phone Number"
                value={userData.phone || 'Not provided'}
                icon={<PhoneIcon className="h-5 w-5" />}
              />
              <ProfileField
                label="Address"
                value={userData.address || 'Not provided'}
                icon={<MapPinIcon className="h-5 w-5" />}
              />
            </ProfileGrid>
          )}
        </ProfileCard>
      </ProfileSection>

      {/* Account Information Section */}
      <ProfileSection
        title="Account Information"
        subtitle="Your account status and verification details"
      >
        <ProfileCard>
          <ProfileGrid cols={2}>
            <ProfileField
              label="Account Status"
              value="Active"
              valueClassName="text-green-600 font-medium"
              icon={<CheckCircleIcon className="h-5 w-5 text-green-500" />}
            />
            <ProfileField
              label="Account Type"
              value="Pet Parent"
              icon={<UserIcon className="h-5 w-5" />}
            />
            <ProfileField
              label="OTP Verification"
              value={userData.is_otp_verified ? "Verified" : "Pending"}
              valueClassName={userData.is_otp_verified ? "text-green-600 font-medium" : "text-orange-600 font-medium"}
              icon={userData.is_otp_verified ?
                <CheckCircleIcon className="h-5 w-5 text-green-500" /> :
                <ExclamationTriangleIcon className="h-5 w-5 text-orange-500" />
              }
            />
            {userData.created_at && (
              <ProfileField
                label="Member Since"
                value={new Date(userData.created_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
                icon={<UserIcon className="h-5 w-5" />}
              />
            )}
          </ProfileGrid>
        </ProfileCard>
      </ProfileSection>

      {/* Information Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <InformationCircleIcon className="h-6 w-6 text-blue-600" />
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-blue-900 mb-2">Profile Information</h3>
            <p className="text-blue-800 text-sm leading-relaxed">
              Your profile information is used to personalize your experience and help cremation service providers
              better serve you. You can update your information anytime using the edit buttons above. Make sure
              your contact information is accurate so service providers can reach you.
            </p>
          </div>
        </div>
      </div>
    </ProfileLayout>
  );
}

export default withUserAuth(ProfilePage);
