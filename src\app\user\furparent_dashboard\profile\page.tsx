'use client';

import React from 'react';
import { UserIcon } from '@heroicons/react/24/outline';
import { UserData } from '@/components/withUserAuth';

interface ProfilePageProps {
  userData?: UserData;
}

function ProfilePage({ userData }: ProfilePageProps) {
  console.log('🔍 [ProfilePage] Component rendered with userData:', userData);

  if (!userData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading profile...</p>
          <p className="text-gray-500 text-sm mt-2">Please wait while we fetch your information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
          <p className="text-gray-600 mt-2">Manage your personal information and preferences</p>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="bg-gradient-to-r from-green-600 to-green-500 px-6 py-8">
            <div className="flex items-center space-x-6">
              <div className="relative">
                <div className="w-24 h-24 rounded-full bg-white/20 flex items-center justify-center">
                  <UserIcon className="h-12 w-12 text-white" />
                </div>
              </div>
              
              <div className="text-white">
                <h2 className="text-2xl font-bold">
                  {userData.first_name || 'User'} {userData.last_name || ''}
                </h2>
                <p className="text-white/80 mt-1">{userData.email || 'No email available'}</p>
                <div className="flex items-center mt-2">
                  <span className="text-sm text-white/90">Account Active</span>
                </div>
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                  Personal Information
                </h3>
                
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500">Full Name</p>
                    <p className="text-gray-900">{userData.first_name || 'User'} {userData.last_name || ''}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-500">Email Address</p>
                    <p className="text-gray-900">{userData.email || 'No email available'}</p>
                  </div>
                  
                  {userData.phone && (
                    <div>
                      <p className="text-sm text-gray-500">Phone Number</p>
                      <p className="text-gray-900">{userData.phone}</p>
                    </div>
                  )}
                  
                  {userData.address && (
                    <div>
                      <p className="text-sm text-gray-500">Address</p>
                      <p className="text-gray-900">{userData.address}</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                  Account Information
                </h3>
                
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500">Account Status</p>
                    <p className="text-green-600 font-medium">Active</p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-500">Account Type</p>
                    <p className="text-gray-900">Pet Parent</p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-500">OTP Verification</p>
                    <p className={userData.is_otp_verified ? "text-green-600 font-medium" : "text-orange-600 font-medium"}>
                      {userData.is_otp_verified ? "Verified" : "Pending"}
                    </p>
                  </div>
                  
                  {userData.created_at && (
                    <div>
                      <p className="text-sm text-gray-500">Member Since</p>
                      <p className="text-gray-900">
                        {new Date(userData.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="flex-1 bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors duration-200">
                  Edit Profile
                </button>
                
                <button className="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors duration-200">
                  Account Settings
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start">
            <div>
              <h3 className="text-lg font-medium text-blue-900 mb-2">Profile Information</h3>
              <p className="text-blue-800 text-sm leading-relaxed">
                Your profile information is used to personalize your experience and help cremation service providers 
                better serve you. You can update your information anytime by clicking the Edit Profile button above.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProfilePage;
